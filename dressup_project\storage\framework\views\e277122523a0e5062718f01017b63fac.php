<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Register</title>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-white h-full">
    <main>
        <?php if (isset($component)) { $__componentOriginal6212aac068a7e6185b0e66fcfa649371 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6212aac068a7e6185b0e66fcfa649371 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.registerform','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('registerform'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6212aac068a7e6185b0e66fcfa649371)): ?>
<?php $attributes = $__attributesOriginal6212aac068a7e6185b0e66fcfa649371; ?>
<?php unset($__attributesOriginal6212aac068a7e6185b0e66fcfa649371); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6212aac068a7e6185b0e66fcfa649371)): ?>
<?php $component = $__componentOriginal6212aac068a7e6185b0e66fcfa649371; ?>
<?php unset($__componentOriginal6212aac068a7e6185b0e66fcfa649371); ?>
<?php endif; ?>
    </main>

</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\dressup_project\resources\views/register.blade.php ENDPATH**/ ?>