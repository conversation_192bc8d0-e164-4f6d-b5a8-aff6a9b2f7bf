# Internationalization (i18n)

Internationalization of Datepicker is done by adding languages' locales (month and day names, button labels, date format and start of the week) to `Datepicker.locales`.  
`en`:_English (US)_ is the pre-installed default language and also used as the fallback language.

> The package includes locale files taken from [bootstrap-datepicker](https://github.com/uxsolutions/bootstrap-datepicker).

## Adding languages

Import the ones you need and merge them with `Datepicker.locales`.

```javascript
import Datepicker from 'path/to/node_modules/vanillajs-datepicker/js/Datepicker.js';
import es from 'path/to/node_modules/vanillajs-datepicker/js/i18n/locales/es.js';
import fr from 'path/to/node_modules/vanillajs-datepicker/js/i18n/locales/fr.js';
import zhCN from 'path/to/node_modules/vanillajs-datepicker/js/i18n/locales/zh-CN.js';

Object.assign(Datepicker.locales, es, fr, zhCN);
```

_Or if you use a bundler that supports [package entry points](https://nodejs.org/api/packages.html#packages_package_entry_points) (e.g. [Rollup](https://rollupjs.org/) with [node-resolve](https://github.com/rollup/plugins/tree/master/packages/node-resolve) plugin v8.4+, [webpack](https://webpack.js.org/) v5+)_

```javascript
import Datepicker from 'vanillajs-datepicker/Datepicker';
import es from 'vanillajs-datepicker/locales/es';
import fr from 'vanillajs-datepicker/locales/fr';
import zhCN from 'vanillajs-datepicker/locales/zh-CN';

Object.assign(Datepicker.locales, es, fr, zhCN);
```

##### for browser

Load the locale files you need after Datepicker

```html
<script src="https://cdn.jsdelivr.net/npm/vanillajs-datepicker@1.1.4/dist/js/datepicker-full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vanillajs-datepicker@1.1.4/dist/js/locales/es.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vanillajs-datepicker@1.1.4/dist/js/locales/fr.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vanillajs-datepicker@1.1.4/dist/js/locales/zh-CN.js"></script>
```

### Custom locale

If needed, you can create your custom locale file by modifying the following template.

```javascript
/**
 * English translation
 */
export default {
  en: {
    days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
    daysShort: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
    daysMin: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
    months: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
    monthsShort: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    today: "Today",
    clear: "Clear",
    titleFormat: "MM y",
    format: "mm/dd/yyyy",
    weekStart: 0
  }
};
```
> You can omit properties that can fallback to `en`:_English (US)_.
>  
> **for browser:**
> ```javascript
> /**
>  * English translation
>  */
> (function () {
>   Datepicker.locales.en = {
>     //... same properties as the above 
>   }
> })();
> ```


Locale must be named with language code. The code can be arbitrary, but should comply with [IETF language tag](https://en.wikipedia.org/wiki/IETF_language_tag).


## Text Direction

Text direction handling of Datepicker is mainly done by stylesheet and completely separated from its language configuration.

The picker element is styled to follow the container element's text direction. If the direction of the associated input field is different from it, date picker automatically detects the difference and sets the picker element's `dir` attribute so that the direction matches the input field's.

> **When customizing the prev/next button**  
>
> The default of the prev/next button arrows are a pair of a parenthesis variant, which automatically flip according to text direction. If you customize the arrows with something other than parenthesis characters, you may need to add a style like below to your project's CSS in order for the arrows to flip automatically.
>
> ```css
> [dir="rtl"] .datepicker-controls .prev-btn,
> [dir="rtl"] .datepicker-controls .next-btn {
>     transform: scaleX(-1);
> }
>```
