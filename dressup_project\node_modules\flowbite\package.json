{"name": "flowbite", "version": "3.1.2", "description": "The most popular library of interactive components built with Tailwind CSS", "keywords": ["flowbite", "typescript", "javascript", "webpack", "html", "css", "component library", "ui components", "tailwind", "tailwind css", "tailwind components", "tailwind elements", "tailwind library", "tailwind sections", "tailwind css", "tailwind ui", "tailwind react", "tailwind vue", "tailwind angular", "tailwind svelte", "tailwind django", "tailwind ruby on rails", "tailwind laravel", "tailwind nuxt", "tailwind next", "tailwind astro", "tailwind flask", "eslint", "prettier", "cjs", "esm", "umd"], "license": "MIT", "main": "lib/cjs/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "style": "dist/flowbite.min.css", "repository": "https://github.com/themesberg/flowbite.git", "bugs": "https://github.com/themesberg/flowbite/issues", "homepage": "https://flowbite.com", "contributors": ["<PERSON><PERSON><PERSON>én<PERSON> (https://x.com/zoltanszogyenyi)", "<PERSON> (https://x.com/robert<PERSON><PERSON>)"], "author": "Bergside Inc.", "scripts": {"start": "cross-env NODE_ENV=development run-p start:*", "start:hugo": "hugo server -D", "start:webpack": "webpack --mode=development --watch", "start:css": "cross-env NPM_ENV=development npx @tailwindcss/cli -i src/flowbite.css -o static/flowbite.css --watch", "start:css-docs": "cross-env NPM_ENV=development npx @tailwindcss/cli -i src/main.css -o static/main.css --watch", "build": "cross-env NODE_ENV=production && HUGO_ENV=production && run-s build:css:docs:min build:webpack build:hugo", "build:hugo": "hugo", "build:webpack": "webpack --mode=production", "build:css:min": "cross-env NPM_ENV=production npx @tailwindcss/cli -i src/flowbite.css -o dist/flowbite.min.css --minify", "build:css": "cross-env NPM_ENV=development npx @tailwindcss/cli -i src/flowbite.css -o dist/flowbite.css", "build:css:docs:min": "cross-env NPM_ENV=production npx @tailwindcss/cli -i src/main.css -o static/main.css --minify", "build:css-docs": "cross-env NPM_ENV=development npx @tailwindcss/cli -i src/main.css -o static/main.css", "build:js": "run-s build:webpack", "copy:js:dist": "copyfiles --flat static/flowbite.js dist && copyfiles --flat static/flowbite.js.map dist && copyfiles --flat static/flowbite.min.js.map dist && copyfiles --flat static/flowbite.min.js dist && copyfiles --flat static/flowbite.turbo.js dist && copyfiles --flat static/flowbite.turbo.min.js dist && copyfiles --flat static/flowbite.turbo.js.map dist && copyfiles --flat static/flowbite.turbo.min.js.map dist && copyfiles --flat static/flowbite.phoenix.js dist && copyfiles --flat static/flowbite.phoenix.min.js dist && copyfiles --flat static/flowbite.phoenix.js.map dist && copyfiles --flat static/flowbite.phoenix.min.js.map dist", "build:dist": "mkdir -p dist && run-s build:css:min build:css build:js copy:js:dist", "build:lib": "tsc --outDir lib/cjs && tsc -m es6 --outDir lib/esm", "clean:lib": "shx rm -rf lib", "clean:dist": "shx rm -rf dist", "build:npm": "run-s clean:lib build:lib clean:dist build:dist", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --ignore-path .gitignore"}, "devDependencies": {"@babel/core": "^7.14.8", "@babel/preset-env": "^7.14.8", "@docsearch/js": "^3.0.0-alpha.42", "@tailwindcss/cli": "^4.0.0", "@tailwindcss/postcss": "^4.0.0", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "autoprefixer": "^10.3.3", "babel-loader": "^8.2.2", "copyfiles": "^2.4.1", "core-js": "^3.8.1", "cross-env": "^7.0.3", "css-loader": "^5.2.7", "css-minimizer-webpack-plugin": "^3.0.2", "cssnano": "^5.0.8", "eslint": "^8.29.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-tailwindcss": "^3.7.1", "file-loader": "^6.2.0", "flowbite-typography": "^1.0.5", "mini-css-extract-plugin": "^1.3.3", "npm-run-all": "^4.1.5", "prettier": "^2.8.1", "prettier-plugin-tailwindcss": "^0.2.1", "shx": "^0.3.4", "source-map-loader": "^2.0.0", "style-loader": "^2.0.0", "tailwindcss": "^4.0.0", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "typescript": "^4.9.4", "webpack": "^5.47.0", "webpack-cli": "^4.7.2", "webpack-dev-server": "^4.11.1", "windicss": "^3.5.6", "yarn": "^1.22.10"}, "dependencies": {"@popperjs/core": "^2.9.3", "flowbite-datepicker": "^1.3.1", "mini-svg-data-uri": "^1.4.3", "postcss": "^8.5.1"}, "files": ["lib", "dist", "src/themes", "types", "plugin.d.ts", "plugin.js", "plugin-windicss.js"]}