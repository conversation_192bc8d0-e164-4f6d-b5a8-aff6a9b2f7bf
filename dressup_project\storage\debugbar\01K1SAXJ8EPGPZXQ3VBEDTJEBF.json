{"__meta": {"id": "01K1SAXJ8EPGPZXQ3VBEDTJEBF", "datetime": "2025-08-04 01:25:14", "utime": **********.128079, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754270693.859086, "end": **********.128103, "duration": 20.269016981124878, "duration_str": "20.27s", "measures": [{"label": "Booting", "start": 1754270693.859086, "relative_start": 0, "end": **********.543816, "relative_end": **********.543816, "duration": 0.***************, "duration_str": "685ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.543837, "relative_start": 0.***************, "end": **********.128107, "relative_end": 4.0531158447265625e-06, "duration": 19.***************, "duration_str": "19.58s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.976932, "relative_start": 3.****************, "end": **********.983959, "relative_end": **********.983959, "duration": 0.0070269107818603516, "duration_str": "7.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.09471, "relative_start": 20.***************, "end": **********.095333, "relative_end": **********.095333, "duration": 0.0006229877471923828, "duration_str": "623μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.121214, "relative_start": 20.**************, "end": **********.121372, "relative_end": **********.121372, "duration": 0.00015807151794433594, "duration_str": "158μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04672, "accumulated_duration_str": "46.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.014878, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "dressup_project", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM' limit 1", "type": "query", "params": [], "bindings": ["exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.018348, "duration": 0.02829, "duration_str": "28.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "dressup_project", "explain": null, "start_percent": 0, "width_percent": 60.552}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.058153, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "dressup_project", "explain": null, "start_percent": 60.552, "width_percent": 2.975}, {"sql": "select count(*) as aggregate from `products` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/product_list_widget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Widgets\\product_list_widget.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1754270706.140228, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "product_list_widget.php:18", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/product_list_widget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Widgets\\product_list_widget.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FWidgets%2Fproduct_list_widget.php&line=18", "ajax": false, "filename": "product_list_widget.php", "line": "18"}, "connection": "dressup_project", "explain": null, "start_percent": 63.527, "width_percent": 3.232}, {"sql": "select count(*) as aggregate from `products` where `user_id` = 1 and `type` = 'Gown'", "type": "query", "params": [], "bindings": [1, "Gown"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/product_list_widget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Widgets\\product_list_widget.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1754270706.1530578, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "product_list_widget.php:20", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/product_list_widget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Widgets\\product_list_widget.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FWidgets%2Fproduct_list_widget.php&line=20", "ajax": false, "filename": "product_list_widget.php", "line": "20"}, "connection": "dressup_project", "explain": null, "start_percent": 66.759, "width_percent": 2.783}, {"sql": "select count(*) as aggregate from `products` where `user_id` = 1 and `type` = 'Suit'", "type": "query", "params": [], "bindings": [1, "Suit"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/product_list_widget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Widgets\\product_list_widget.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1754270706.160767, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "product_list_widget.php:22", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/product_list_widget.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Widgets\\product_list_widget.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FWidgets%2Fproduct_list_widget.php&line=22", "ajax": false, "filename": "product_list_widget.php", "line": "22"}, "connection": "dressup_project", "explain": null, "start_percent": 69.542, "width_percent": 3.061}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiN0xWUTNiWEFFRm1VRnJrZ3dZcFJLdU12cWROQkJWV01RMVVjaHQwciI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjI3OiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvYWRtaW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkdTJ3L2NZU041dHA4N01Qd0MuWmg2LmpWSlM0UkdLTGIzUTNNVjNhZXZ6aHZuMkNJS0FmeEsiO3M6MjI6IlBIUERFQlVHQkFSX1NUQUNLX0RBVEEiO2E6MDp7fX0=', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiN0xWUTNiWEFFRm1VRnJrZ3dZcFJLdU12cWROQkJWV01RMVVjaHQwciI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjI3OiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvYWRtaW4iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aToxO3M6MTc6InBhc3N3b3JkX2hhc2hfd2ViIjtzOjYwOiIkMnkkMTIkdTJ3L2NZU041dHA4N01Qd0MuWmg2LmpWSlM0UkdLTGIzUTNNVjNhZXZ6aHZuMkNJS0FmeEsiO3M6MjI6IlBIUERFQlVHQkFSX1NUQUNLX0RBVEEiO2E6MDp7fX0=", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.098859, "duration": 0.0128, "duration_str": "12.8ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "dressup_project", "explain": null, "start_percent": 72.603, "width_percent": 27.397}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"filament.pages.dashboard #G28YyEYxyYehGr4hZQl7": "array:4 [\n  \"data\" => array:14 [\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"filament.pages.dashboard\"\n  \"component\" => \"Filament\\Pages\\Dashboard\"\n  \"id\" => \"G28YyEYxyYehGr4hZQl7\"\n]", "filament.widgets.account-widget #DKVbp2KOUHQLMe5hSLE1": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament.widgets.account-widget\"\n  \"component\" => \"Filament\\Widgets\\AccountWidget\"\n  \"id\" => \"DKVbp2KOUHQLMe5hSLE1\"\n]", "app.filament.widgets.product_list_widget #3EQOqEGeGYm9h1RCAYVf": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.product_list_widget\"\n  \"component\" => \"App\\Filament\\Widgets\\product_list_widget\"\n  \"id\" => \"3EQOqEGeGYm9h1RCAYVf\"\n]", "filament.livewire.global-search #jSUVJCwLxtQt0VNqVAYL": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"jSUVJCwLxtQt0VNqVAYL\"\n]", "filament.livewire.notifications #2lxSom5SnEWYgYgtcDDI": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2647\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"2lxSom5SnEWYgYgtcDDI\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard", "uri": "GET admin", "controller": "Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "20.27s", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkV5RlYrR0hiZllGUTJGWklseTVGcGc9PSIsInZhbHVlIjoiQkZJOFIxZlp1V2h4UVNMT3RkRExMbHp2TjFodEtkM3hYN2Q4NHF2RTlXcGxGVVY5Znc4SmhteUE4SDdCZy9EM1VBSVRrTTRTT0dFelZiRG5pNjNGT2RhalZ4WUlhZ1ZGTVZHa1VSUlhFNjJFOGkvVXNSMHNSaVFPb29sVkVrVHgiLCJtYWMiOiJlN2RiODAxMWMxMDI3MTQwZjNlMzkzYTRjNDgzNjgyN2Y0ZTcwYzY0OWZiNTRjZjQzZjcyOTExOGFmYzViOGE5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Iko2a09UVk9pQkMxMGlyU0dlbHNGVFE9PSIsInZhbHVlIjoiZExqLzBLd0toTmQvS2wyeDF6QWV1dXc3TEFPS3NHbUFlQ0lJaDdBdHY5a0hwU1lqZ0xJRnlEQVJwNG04Z2xKWFJiM1Rpb1ZqMTBJaG81ZW5GQ3o3OUJPKzBSbE1vMEtUOG9nbzRVc3BHMlpTQTRCSS9VUllCUklvajFrWC9md20iLCJtYWMiOiI5ZDYwZGQ0YWMxNTM5ZjA0ZmFmM2Q5Mzk4NmUwNWYwMmY2NWNkOWJlYzY2MjRjOGEzZmNmOThiOTMzMDU5NmI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-877603885 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7LVQ3bXAEFmUFrkgwYpRKuMvqdNBBVWMQ1Ucht0r</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-877603885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-180601274 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 01:25:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkJwVStEcU0zdDBBN3owUHpkdTFEUXc9PSIsInZhbHVlIjoiV3ZDR3VSZmNoWkZPOWl6MnI1YTBFSGxWdk15cHBrOXFPNU5NYzR3WEJSbGNYK3NyV2VtZEtCaGdWaGNGY2N2YndLSnpEL2RoV1JwN1hXaXBJQ0tCTzdwcXY2UW0wUlhodUV5TDNidHhlUzE2UGlGd2VjdWFIQzlCc0hYL01EWnUiLCJtYWMiOiI5MWU4OGFkYWYzNjMyNTQyODEwYjg5OTZlZjZiODRhYjg3ZmY1ZDRjYzlhMGJiYzNjNzI2ODgzMGFmOGNjODUzIiwidGFnIjoiIn0%3D; expires=Mon, 04 Aug 2025 03:25:14 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImVCanEvQ3drZVpHUWtoYU0xZGE2V0E9PSIsInZhbHVlIjoiYTl3VFVVUUU5ZGhoZ3I1MlRqN0o2bU13emRGZG5XbzJsZTdYV090WnRJdThWZnJyS2hObWJ1ZS9RM2QxZU9JZ2xJajBEQWlJYVRRS0lhY2VENkcrdlIzMytFemlXNWpPUFJOSEJ1VVlHQjFpUUZWZ1FkM3g0cmNpWjlmZzlGNkQiLCJtYWMiOiIyOWM2NGJjOGY5MGM5MTczNWJkMzYxOGVlMDA0NDhiODg3NDY2YmFmYzYwMTZkZjE2ZWNkYTZmMjk4NjA0ZGY2IiwidGFnIjoiIn0%3D; expires=Mon, 04 Aug 2025 03:25:14 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkJwVStEcU0zdDBBN3owUHpkdTFEUXc9PSIsInZhbHVlIjoiV3ZDR3VSZmNoWkZPOWl6MnI1YTBFSGxWdk15cHBrOXFPNU5NYzR3WEJSbGNYK3NyV2VtZEtCaGdWaGNGY2N2YndLSnpEL2RoV1JwN1hXaXBJQ0tCTzdwcXY2UW0wUlhodUV5TDNidHhlUzE2UGlGd2VjdWFIQzlCc0hYL01EWnUiLCJtYWMiOiI5MWU4OGFkYWYzNjMyNTQyODEwYjg5OTZlZjZiODRhYjg3ZmY1ZDRjYzlhMGJiYzNjNzI2ODgzMGFmOGNjODUzIiwidGFnIjoiIn0%3D; expires=Mon, 04-Aug-2025 03:25:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImVCanEvQ3drZVpHUWtoYU0xZGE2V0E9PSIsInZhbHVlIjoiYTl3VFVVUUU5ZGhoZ3I1MlRqN0o2bU13emRGZG5XbzJsZTdYV090WnRJdThWZnJyS2hObWJ1ZS9RM2QxZU9JZ2xJajBEQWlJYVRRS0lhY2VENkcrdlIzMytFemlXNWpPUFJOSEJ1VVlHQjFpUUZWZ1FkM3g0cmNpWjlmZzlGNkQiLCJtYWMiOiIyOWM2NGJjOGY5MGM5MTczNWJkMzYxOGVlMDA0NDhiODg3NDY2YmFmYzYwMTZkZjE2ZWNkYTZmMjk4NjA0ZGY2IiwidGFnIjoiIn0%3D; expires=Mon, 04-Aug-2025 03:25:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180601274\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-856444938 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7LVQ3bXAEFmUFrkgwYpRKuMvqdNBBVWMQ1Ucht0r</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$u2w/cYSN5tp87MPwC.Zh6.jVJS4RGKLb3Q3MV3aevzhvn2CIKAfxK</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856444938\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard"}, "badge": null}}