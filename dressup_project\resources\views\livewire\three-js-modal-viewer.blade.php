<div x-data="{
        // Three.js variables
        scene: null,
        camera: null,
        renderer: null,
        controls: null,
        model: null,
        containerRef: null,
        clippingPlanes: [], // Array of THREE.Plane objects

        // Livewire data binding
        modelUrl: @js($this->modelUrl), // Passed from Livewire property
        clipSize: $wire.entangle('data.clipSize'),
        clipPosX: $wire.entangle('data.clipPosX'),
        clipPosY: $wire.entangle('data.clipPosY'),
        clipPosZ: $wire.entangle('data.clipPosZ'),
        enableClipping: $wire.entangle('data.enableClipping'),

        init() {
            this.$nextTick(() => {
                this.containerRef = this.$refs.viewerContainer;
                const width = this.containerRef.clientWidth;
                const height = this.containerRef.clientHeight;

                // 1. Scene setup
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x222222);

                // 2. Camera setup
                this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
                this.camera.position.set(0, 5, 10);

                // 3. Renderer setup
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setPixelRatio(window.devicePixelRatio);
                this.renderer.setSize(width, height);
                this.containerRef.appendChild(this.renderer.domElement);
                this.renderer.localClippingEnabled = true; // Enable local clipping

                // 4. Lights
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
                this.scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(5, 10, 7).normalize();
                this.scene.add(directionalLight);

                // 5. OrbitControls
                this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
                this.controls.enableDamping = true;
                this.controls.dampingFactor = 0.25;
                this.controls.screenSpacePanning = false;
                this.controls.minDistance = 1;
                this.controls.maxDistance = 500;
                this.controls.maxPolarAngle = Math.PI / 2;

                // 6. Load Model
                this.loadModel(this.modelUrl);

                // 7. Animation Loop
                const animate = () => {
                    requestAnimationFrame(animate);
                    this.controls.update();
                    this.renderer.render(this.scene, this.camera);
                };
                animate();

                // 8. Handle Window Resizing (important for modals which might change size)
                window.addEventListener('resize', () => {
                    if (this.containerRef) { // Check if container still exists
                        const newWidth = this.containerRef.clientWidth;
                        const newHeight = this.containerRef.clientHeight;
                        this.camera.aspect = newWidth / newHeight;
                        this.camera.updateProjectionMatrix();
                        this.renderer.setSize(newWidth, newHeight);
                    }
                });

                // Watch for changes in Livewire bound properties
                this.$watch('clipSize', () => this.updateClippingPlanes());
                this.$watch('clipPosX', () => this.updateClippingPlanes());
                this.$watch('clipPosY', () => this.updateClippingPlanes());
                this.$watch('clipPosZ', () => this.updateClippingPlanes());
                this.$watch('enableClipping', () => this.updateClippingPlanes());

                this.updateClippingPlanes(); // Initial clipping setup
            });
        },

        loadModel(url) {
            if (this.model) {
                this.scene.remove(this.model);
                this.model.traverse(child => { // Dispose of old materials/geometries
                    if (child.isMesh) {
                        child.geometry.dispose();
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                });
                this.model = null; // Clear reference
            }

            if (!url) {
                console.warn('No model URL provided for loading.');
                return;
            }

            const loader = new THREE.GLTFLoader();
            loader.load(url, (gltf) => {
                this.model = gltf.scene;
                this.scene.add(this.model);

                // Fit camera to the newly loaded model
                this.fitCameraToObject(this.model, 1.5); // 1.5 is padding

                // Apply clipping planes to the model's materials
                this.updateClippingPlanes();

            }, undefined, (error) => {
                console.error('An error occurred while loading the model:', error);
                // Optionally show a notification
                // Livewire.dispatch('filament::notify', { type: 'danger', title: 'Error loading 3D model.', body: 'Check console for details.' });
            });
        },

        fitCameraToObject(object, offset = 1.2) {
            const box = new THREE.Box3().setFromObject(object);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());
            const maxDim = Math.max(size.x, size.y, size.z);
            const fov = this.camera.fov * (Math.PI / 180);
            let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
            cameraZ *= offset;

            this.camera.position.set(center.x, center.y, center.z + cameraZ);
            this.controls.target.copy(center);
            this.controls.update();
        },

        updateClippingPlanes() {
            if (!this.renderer || !this.model) {
                return;
            }

            if (!this.enableClipping) {
                this.clippingPlanes = []; // Disable clipping
            } else {
                const halfSize = this.clipSize / 2;
                this.clippingPlanes = [
                    new THREE.Plane(new THREE.Vector3(1, 0, 0), -halfSize + this.clipPosX),  // Left Plane
                    new THREE.Plane(new THREE.Vector3(-1, 0, 0), -halfSize - this.clipPosX), // Right Plane
                    new THREE.Plane(new THREE.Vector3(0, 1, 0), -halfSize + this.clipPosY),  // Bottom Plane
                    new THREE.Plane(new THREE.Vector3(0, -1, 0), -halfSize - this.clipPosY), // Top Plane
                    new THREE.Plane(new THREE.Vector3(0, 0, 1), -halfSize + this.clipPosZ),  // Back Plane
                    new THREE.Plane(new THREE.Vector3(0, 0, -1), -halfSize - this.clipPosZ), // Front Plane
                ];
            }

            this.renderer.clippingPlanes = this.clippingPlanes;

            // Apply clipping planes to all materials in the model
            this.model.traverse((object) => {
                if (object.isMesh) {
                    const material = object.material;
                    if (Array.isArray(material)) {
                        material.forEach(mat => {
                            mat.clippingPlanes = this.clippingPlanes;
                            mat.needsUpdate = true; // Crucial for update
                        });
                    } else if (material) {
                        material.clippingPlanes = this.clippingPlanes;
                        material.needsUpdate = true; // Crucial for update
                    }
                }
            });
        }
    }" wire:ignore {{-- Crucial: Prevents Livewire from re-rendering and destroying the canvas --}}>
    <div class="fi-modal-content-wrapper flex flex-col gap-y-4">
        {{-- Filament Form for controls --}}
        {{ $this->form }}

        {{-- 3D Viewer Container --}}
        <div x-ref="viewerContainer" class="w-full h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
            {{-- Three.js canvas will be appended here --}}
        </div>
    </div>

    <script type="module">
        import * as THREE from 'https://cdn.jsdelivr.net/npm/three@0.159/build/three.module.js';
        import { GLTFLoader } from 'https://cdn.jsdelivr.net/npm/three@0.159/examples/jsm/loaders/GLTFLoader.js';
        import { OrbitControls } from 'https://cdn.jsdelivr.net/npm/three@0.159/examples/jsm/controls/OrbitControls.js';

        // Expose them globally for Alpine.js to access easily
        window.THREE = THREE;
        window.GLTFLoader = GLTFLoader;
        window.OrbitControls = OrbitControls;
    </script>
</div>