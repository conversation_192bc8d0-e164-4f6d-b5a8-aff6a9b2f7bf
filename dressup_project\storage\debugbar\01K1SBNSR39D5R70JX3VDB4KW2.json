{"__meta": {"id": "01K1SBNSR39D5R70JX3VDB4KW2", "datetime": "2025-08-04 01:38:28", "utime": **********.227957, "method": "GET", "uri": "/admin/model-management/attach3d-model-to-products", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.125797, "end": **********.227978, "duration": 2.1021809577941895, "duration_str": "2.1s", "measures": [{"label": "Booting", "start": **********.125797, "relative_start": 0, "end": **********.704943, "relative_end": **********.704943, "duration": 0.****************, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.704956, "relative_start": 0.****************, "end": **********.22798, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.52s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.80102, "relative_start": 1.****************, "end": **********.806963, "relative_end": **********.806963, "duration": 0.0059430599212646484, "duration_str": "5.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.204925, "relative_start": 2.****************, "end": **********.20547, "relative_end": **********.20547, "duration": 0.0005450248718261719, "duration_str": "545μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.22287, "relative_start": 2.****************, "end": **********.223009, "relative_end": **********.223009, "duration": 0.00013899803161621094, "duration_str": "139μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.15.0", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "queries": {"count": 13, "nb_statements": 12, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.041639999999999996, "accumulated_duration_str": "41.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.828503, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "dressup_project", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM' limit 1", "type": "query", "params": [], "bindings": ["exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.830427, "duration": 0.016149999999999998, "duration_str": "16.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "dressup_project", "explain": null, "start_percent": 0, "width_percent": 38.785}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.861344, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "dressup_project", "explain": null, "start_percent": 38.785, "width_percent": 2.017}, {"sql": "select count(*) as aggregate from `products` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.96432, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "dressup_project", "explain": null, "start_percent": 40.802, "width_percent": 2.882}, {"sql": "select * from `products` where `user_id` = 1 order by `products`.`product_id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.968021, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "dressup_project", "explain": null, "start_percent": 43.684, "width_percent": 3.338}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.971232, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "dressup_project", "explain": null, "start_percent": 47.022, "width_percent": 3.098}, {"sql": "select * from `product_3d_models` where `product_3d_models`.`product_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.974353, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "dressup_project", "explain": null, "start_percent": 50.12, "width_percent": 4.251}, {"sql": "select exists(select * from `product_3d_models` where `product_3d_models`.`product_id` = 1 and `product_3d_models`.`product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 128}], "start": **********.036344, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Attach3dModelToProductResource.php:111", "source": {"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FClusters%2Fmodel_management%2FResources%2FAttach3dModelToProductResource.php&line=111", "ajax": false, "filename": "Attach3dModelToProductResource.php", "line": "111"}, "connection": "dressup_project", "explain": null, "start_percent": 54.371, "width_percent": 3.482}, {"sql": "select exists(select * from `product_3d_models` where `product_3d_models`.`product_id` = 1 and `product_3d_models`.`product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.041514, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Attach3dModelToProductResource.php:111", "source": {"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FClusters%2Fmodel_management%2FResources%2FAttach3dModelToProductResource.php&line=111", "ajax": false, "filename": "Attach3dModelToProductResource.php", "line": "111"}, "connection": "dressup_project", "explain": null, "start_percent": 57.853, "width_percent": 3.794}, {"sql": "select exists(select * from `product_3d_models` where `product_3d_models`.`product_id` = 2 and `product_3d_models`.`product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 128}], "start": **********.0656269, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Attach3dModelToProductResource.php:111", "source": {"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FClusters%2Fmodel_management%2FResources%2FAttach3dModelToProductResource.php&line=111", "ajax": false, "filename": "Attach3dModelToProductResource.php", "line": "111"}, "connection": "dressup_project", "explain": null, "start_percent": 61.647, "width_percent": 3.458}, {"sql": "select exists(select * from `product_3d_models` where `product_3d_models`.`product_id` = 2 and `product_3d_models`.`product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeDisabled.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeDisabled.php", "line": 20}], "start": **********.0698261, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Attach3dModelToProductResource.php:111", "source": {"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FClusters%2Fmodel_management%2FResources%2FAttach3dModelToProductResource.php&line=111", "ajax": false, "filename": "Attach3dModelToProductResource.php", "line": "111"}, "connection": "dressup_project", "explain": null, "start_percent": 65.106, "width_percent": 3.194}, {"sql": "select exists(select * from `product_3d_models` where `product_3d_models`.`product_id` = 3 and `product_3d_models`.`product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 119}, {"index": 17, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/filament/actions/src/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\filament\\actions\\src\\Concerns\\CanBeHidden.php", "line": 128}], "start": **********.092087, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Attach3dModelToProductResource.php:111", "source": {"index": 14, "namespace": null, "name": "app/Filament/Clusters/model_management/Resources/Attach3dModelToProductResource.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\app\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FFilament%2FClusters%2Fmodel_management%2FResources%2FAttach3dModelToProductResource.php&line=111", "ajax": false, "filename": "Attach3dModelToProductResource.php", "line": "111"}, "connection": "dressup_project", "explain": null, "start_percent": 68.3, "width_percent": 3.194}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoiN0xWUTNiWEFFRm1VRnJrZ3dZcFJLdU12cWROQkJWV01RMVVjaHQwciI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjcxOiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvYWRtaW4vbW9kZWwtbWFuYWdlbWVudC9hdHRhY2gzZC1tb2RlbC10by1wcm9kdWN0cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiR1MncvY1lTTjV0cDg3TVB3Qy5aaDYualZKUzRSR0tMYjNRM01WM2Fldnpodm4yQ0lLQWZ4SyI7czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoiN0xWUTNiWEFFRm1VRnJrZ3dZcFJLdU12cWROQkJWV01RMVVjaHQwciI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjcxOiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvYWRtaW4vbW9kZWwtbWFuYWdlbWVudC9hdHRhY2gzZC1tb2RlbC10by1wcm9kdWN0cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiR1MncvY1lTTjV0cDg3TVB3Qy5aaDYualZKUzRSR0tMYjNRM01WM2Fldnpodm4yQ0lLQWZ4SyI7czoyMjoiUEhQREVCVUdCQVJfU1RBQ0tfREFUQSI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 139}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 175}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 244}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 129}], "start": **********.206723, "duration": 0.011869999999999999, "duration_str": "11.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:172", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\dressup_project\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=172", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "172"}, "connection": "dressup_project", "explain": null, "start_percent": 71.494, "width_percent": 28.506}]}, "models": {"data": {"App\\Models\\product_image": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FModels%2Fproduct_image.php&line=1", "ajax": false, "filename": "product_image.php", "line": "?"}}, "App\\Models\\product": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FModels%2Fproduct.php&line=1", "ajax": false, "filename": "product.php", "line": "?"}}, "App\\Models\\product_3d_model": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FModels%2Fproduct_3d_model.php&line=1", "ajax": false, "filename": "product_3d_model.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 15, "is_counter": true}, "livewire": {"data": {"app.filament.clusters.model_management.resources.attach3d-model-to-product-resource.pages.list-attach3d-model-to-products #WEWd9VjhPuUUhPpb3vIj": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => null\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.clusters.model_management.resources.attach3d-model-to-product-resource.pages.list-attach3d-model-to-products\"\n  \"component\" => \"App\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource\\Pages\\ListAttach3dModelToProducts\"\n  \"id\" => \"WEWd9VjhPuUUhPpb3vIj\"\n]", "filament.livewire.global-search #NtgsjxR7lcretaWukMLV": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"NtgsjxR7lcretaWukMLV\"\n]", "filament.livewire.notifications #wAuLxHsuuO4wTGTZmLKZ": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2903\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"wAuLxHsuuO4wTGTZmLKZ\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/model-management/attach3d-model-to-products", "action_name": "filament.admin.model-management.resources.attach3d-model-to-products.index", "controller_action": "App\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource\\Pages\\ListAttach3dModelToProducts", "uri": "GET admin/model-management/attach3d-model-to-products", "controller": "App\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource\\Pages\\ListAttach3dModelToProducts@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/model-management/attach3d-model-to-products", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FCharl%2FDesktop%2Fdressup_project%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "2.1s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">http://127.0.0.1:8000/admin/model-management/create-3d-models</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVzTVhGbzdHeC95LzRyTHE5S1RRWGc9PSIsInZhbHVlIjoiV2FXb2t2bGlEQlhJbkZvWU03L3JnVTF1clB6YTB5dE1zWk5nOFlHazcrc2dtaFczbldibk1JSXRrQmJuaWRTVFZxKzdNK1cyUzFJUG1LZGNFQkJXeVFYVUJrWW5NR2ZTVFllTmJvZldCYjV0cXVLRU82V3RqdVJ2MUxoSFJZZDYiLCJtYWMiOiJmM2JiZDM0YTc0MTAwZmEyMDY3NTEwNmM4Mjg5MGNmNDliNWM2MzYyYTY1MDk2NWJlM2JkMjViZGJlY2IxMWY1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImdVYkVESXp5RHNXTm80MmVXdFByN0E9PSIsInZhbHVlIjoiNzA5MFQ4R2xidkRld3pSaisrREhILy9oNTUxM3VJY2VuVFdIRFV2YmFXWDA5RkJFaU9tRlhLL05LRHc3aE50N05FSG9GLzcyRENhYUo2Ym1TeENPa1BYYVBmenhiYjlySC9hbWdHUlRPNjU4dVJMOFptQlFqVVUzNXpiZEhHcGgiLCJtYWMiOiJjMzBmZTM4NDQzYzI0ZDU0YTYzYWQzNDYzMzBlNjYwYjQ3MzdjNDA5ZDMwNDI4YmFmYzFlYjJiZGUwYjQzYzk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1273318558 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7LVQ3bXAEFmUFrkgwYpRKuMvqdNBBVWMQ1Ucht0r</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">exbOLj1s4muHGKKLwA4LzbDB2i7hgWcT7FVy1WHM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273318558\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1713207316 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 01:38:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im02dllzK1R5RXNJc2drTWIvWU9rR1E9PSIsInZhbHVlIjoiVVVBYkdNbzlBM0JIVmZ6ZE9lQWVCZkdpSGJwdi9rSjNmOGFhMWJNcTk2MEZuUWtlb2ZFQU9la216eG82bFRlb1ZxcFVCVlVaM0wwSDYzTzNJckdlVUNRVnBEUThPT0FiQ3hoVjFtU3ZvTmpka21ibU52SzNLVkI3aFpweWtQcWMiLCJtYWMiOiI0NTU2NDNhMGQ5MDVjMjc5Y2M1YjEzMDkwNjQxMzlmOGRlNTVhZTZhYTc2MDVkZDY3MTI3ODI5MmRiMGNiODcwIiwidGFnIjoiIn0%3D; expires=Mon, 04 Aug 2025 03:38:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IjR2M2FHblRoSkRjVDJIcjhyOHA4anc9PSIsInZhbHVlIjoiMXoxUk80MHFIV2VKM085Zm5MeW4vOHFPTkdwTnFKV1phQzRtbXdjL0VFVUllT2o4SkF0L3ZFR3JrdmJMeHl2MzJ2OW4zbklRVTFYelZORkdVMkg3bnMxcEhwMW91Yi9wRmxONzR3MDFsWnhWWVg3VG9JTzZqcWRGQVQxeEp3SEoiLCJtYWMiOiJhOTRkMTczMzc5MDc2MTFjMjI4ZGM5ZWE3ODgzMjMxZGQyMjhlMGI2ZGQxYTRkNTIzMDY3ZTVmY2M4NmZiNzQzIiwidGFnIjoiIn0%3D; expires=Mon, 04 Aug 2025 03:38:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im02dllzK1R5RXNJc2drTWIvWU9rR1E9PSIsInZhbHVlIjoiVVVBYkdNbzlBM0JIVmZ6ZE9lQWVCZkdpSGJwdi9rSjNmOGFhMWJNcTk2MEZuUWtlb2ZFQU9la216eG82bFRlb1ZxcFVCVlVaM0wwSDYzTzNJckdlVUNRVnBEUThPT0FiQ3hoVjFtU3ZvTmpka21ibU52SzNLVkI3aFpweWtQcWMiLCJtYWMiOiI0NTU2NDNhMGQ5MDVjMjc5Y2M1YjEzMDkwNjQxMzlmOGRlNTVhZTZhYTc2MDVkZDY3MTI3ODI5MmRiMGNiODcwIiwidGFnIjoiIn0%3D; expires=Mon, 04-Aug-2025 03:38:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IjR2M2FHblRoSkRjVDJIcjhyOHA4anc9PSIsInZhbHVlIjoiMXoxUk80MHFIV2VKM085Zm5MeW4vOHFPTkdwTnFKV1phQzRtbXdjL0VFVUllT2o4SkF0L3ZFR3JrdmJMeHl2MzJ2OW4zbklRVTFYelZORkdVMkg3bnMxcEhwMW91Yi9wRmxONzR3MDFsWnhWWVg3VG9JTzZqcWRGQVQxeEp3SEoiLCJtYWMiOiJhOTRkMTczMzc5MDc2MTFjMjI4ZGM5ZWE3ODgzMjMxZGQyMjhlMGI2ZGQxYTRkNTIzMDY3ZTVmY2M4NmZiNzQzIiwidGFnIjoiIn0%3D; expires=Mon, 04-Aug-2025 03:38:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713207316\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-89882319 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7LVQ3bXAEFmUFrkgwYpRKuMvqdNBBVWMQ1Ucht0r</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://127.0.0.1:8000/admin/model-management/attach3d-model-to-products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$u2w/cYSN5tp87MPwC.Zh6.jVJS4RGKLb3Q3MV3aevzhvn2CIKAfxK</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89882319\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/model-management/attach3d-model-to-products", "action_name": "filament.admin.model-management.resources.attach3d-model-to-products.index", "controller_action": "App\\Filament\\Clusters\\model_management\\Resources\\Attach3dModelToProductResource\\Pages\\ListAttach3dModelToProducts"}, "badge": null}}