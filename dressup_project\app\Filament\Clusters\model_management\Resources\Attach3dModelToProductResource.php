<?php

namespace App\Filament\Clusters\model_management\Resources;

use App\Filament\Clusters\model_management\Resources\Attach3dModelToProductResource\Pages;
use App\Filament\Clusters\model_management;
use App\Models\product;
use App\Models\product_3d_model;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;  // Import the View contract
// use Illuminate\View\View; // No longer needed if using Illuminate\Contracts\View\View
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
// Add the Livewire component import
use App\Livewire\ThreeJsModalViewer;

class Attach3dModelToProductResource extends Resource
{
    protected static ?string $model = product::class;
    protected static ?string $navigationIcon = 'heroicon-o-paper-clip';
    protected static ?string $cluster = model_management::class;
    protected static ?string $navigationLabel = 'Attach 3D Model to Product';

    public static function getTitle(): string
    {
        return 'Attach 3D Model to Product';
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canViewAny(): bool
    {
        return Auth::user()?->isAdmin();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }

    public static function getTableQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id())
            ->with(['product_images', 'product_3d_models']);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('product_images.image_path')
                    ->label('Image')
                    ->height(150)
                    ->width(120)
                    ->limit(1),
                TextColumn::make('name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('type')
                    ->label('Type')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('subtype')
                    ->label('Subtype')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('product_3d_models.model_path')
                    ->label('Attached 3D Model')
                    ->formatStateUsing(function (?string $state): string {
                        return $state ? 'Yes' : 'No';
                    })
                    ->badge()
                    ->color(function (?string $state): string {
                        return $state ? 'success' : 'danger';
                    })
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Action::make('view3dModel')
                    ->label('View 3D Model')
                    ->icon('heroicon-o-eye')
                    ->visible(fn(Product $record) => $record->product_3d_models()->exists())
                    ->modalHeading(fn(Product $record) => 'View 3D Model: ' . $record->name)
                    ->modalWidth('4xl')
                    // --- MODIFIED HERE ---
                    ->modalContent(function (Product $record): View {  // Type-hint changed to View
                        $modelUrl = asset('storage/' . $record->product_3d_models->model_path);
                        // Return a view, passing the modelUrl as data
                        return view('filament.modals.three-js-viewer-modal', ['modelUrl' => $modelUrl]);
                    })
                    // --- END MODIFIED ---
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close'),
                Action::make('attach3dModel')
                    ->label('Attach 3D Model')
                    ->icon('heroicon-o-cube')
                    ->modalHeading(fn(Product $record) => 'Attach 3D Model to ' . $record->name)
                    ->form(function (Product $record): array {
                        return [
                            Fieldset::make('Product Details')
                                ->schema([
                                    TextInput::make('name')
                                        ->default($record->name)
                                        ->disabled(),
                                    TextInput::make('type')
                                        ->default($record->type)
                                        ->disabled(),
                                    TextInput::make('subtype')
                                        ->default($record->subtype)
                                        ->disabled(),
                                ])
                                ->columns(3),
                            FileUpload::make('model_file')
                                ->label('Upload 3D Model File (.glb, .gltf, .obj)')
                                ->acceptedFileTypes(['model/gltf-binary', 'model/gltf+json', 'model/obj'])
                                ->disk('public')
                                ->directory('product-models')
                                ->required()
                                ->appendFiles()
                                ->preserveFileNames(),
                        ];
                    })
                    ->action(function (array $data, Product $record): void {
                        try {
                            $modelPath = $data['model_file'];

                            $existingModel = $record->product_3d_models;

                            if ($existingModel) {
                                $existingModel->update(['model_path' => $modelPath]);
                                $message = '3D model updated successfully!';
                            } else {
                                product_3d_model::create([
                                    'product_id' => $record->product_id,
                                    'model_path' => $modelPath,
                                ]);
                                $message = '3D model attached successfully!';
                            }

                            Notification::make()
                                ->title($message)
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Failed to attach 3D model')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                            Log::error('Failed to attach 3D model: ' . $e->getMessage());
                        }
                    })
                    ->modalSubmitActionLabel('Attach Model')
                    ->modalCancelActionLabel('Cancel'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAttach3dModelToProducts::route('/'),
        ];
    }
}
