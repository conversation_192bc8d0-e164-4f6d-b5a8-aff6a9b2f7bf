<?php

namespace App\Livewire;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\NumericInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class ThreeJsModalViewer extends Component implements HasForms
{
    use InteractsWithForms;

    public $modelUrl;
    public $initialClipSize = 2.0;
    public $initialClipPosX = 0.0;
    public $initialClipPosY = 0.0;
    public $initialClipPosZ = 0.0;
    public $initialEnableClipping = false;
    public float $clipSize;
    public float $clipPosX;
    public float $clipPosY;
    public float $clipPosZ;
    public bool $enableClipping;
    public array $data = [];

    protected $listeners = ['openThreeJsModal' => 'mount'];

    public function mount($modelUrl, $initialClippingData = null)
    {
        $this->modelUrl = $modelUrl;

        if ($initialClippingData) {
            $this->initialClipSize = $initialClippingData['clipSize'] ?? 2.0;
            $this->initialClipPosX = $initialClippingData['clipPosX'] ?? 0.0;
            $this->initialClipPosY = $initialClippingData['clipPosY'] ?? 0.0;
            $this->initialClipPosZ = $initialClippingData['clipPosZ'] ?? 0.0;
            $this->initialEnableClipping = $initialClippingData['enableClipping'] ?? false;
        }

        $this->form->fill([
            'clipSize' => $this->initialClipSize,
            'clipPosX' => $this->initialClipPosX,
            'clipPosY' => $this->initialClipPosY,
            'clipPosZ' => $this->initialClipPosZ,
            'enableClipping' => $this->initialEnableClipping,
        ]);

        $this->clipSize = $this->initialClipSize;
        $this->clipPosX = $this->initialClipPosX;
        $this->clipPosY = $this->initialClipPosY;
        $this->clipPosZ = $this->initialClipPosZ;
        $this->enableClipping = $this->initialEnableClipping;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Clipping Controls')
                    ->description('Adjust the virtual cube to isolate parts of the model.')
                    ->schema([
                        Toggle::make('enableClipping')
                            ->label('Enable Clipping Cube')
                            ->live()
                            ->default(false),
                        Grid::make(2)
                            ->visible(fn($get) => $get('enableClipping'))
                            ->schema([
                                // *** CRITICAL: Ensure these are NumericInput, NOT TextInput ***
                                NumericInput::make('clipSize')  // <--- MUST BE NumericInput
                                    ->label('Cube Size')
                                    ->step(0.1)
                                    ->min(0.5)
                                    ->max(10)
                                    ->default(2.0)
                                    ->live(),
                                NumericInput::make('clipPosX')  // <--- MUST BE NumericInput
                                    ->label('Cube Position X')
                                    ->step(0.1)
                                    ->min(-5)
                                    ->max(5)
                                    ->default(0.0)
                                    ->live(),
                                NumericInput::make('clipPosY')  // <--- MUST BE NumericInput
                                    ->label('Cube Position Y')
                                    ->step(0.1)
                                    ->min(-5)
                                    ->max(5)
                                    ->default(0.0)
                                    ->live(),
                                NumericInput::make('clipPosZ')  // <--- MUST BE NumericInput
                                    ->label('Cube Position Z')
                                    ->step(0.1)
                                    ->min(-5)
                                    ->max(5)
                                    ->default(0.0)
                                    ->live(),
                            ]),
                    ]),
            ])
            ->statePath('data');
    }

    public function render()
    {
        return view('livewire.three-js-modal-viewer');
    }
}
