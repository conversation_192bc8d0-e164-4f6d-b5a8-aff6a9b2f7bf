<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Login</title>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>

<body class="bg-white h-full">
    <main>
        <?php if (isset($component)) { $__componentOriginaldf267884bfc4ded59a941fd476ce5ead = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaldf267884bfc4ded59a941fd476ce5ead = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.loginform','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('loginform'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaldf267884bfc4ded59a941fd476ce5ead)): ?>
<?php $attributes = $__attributesOriginaldf267884bfc4ded59a941fd476ce5ead; ?>
<?php unset($__attributesOriginaldf267884bfc4ded59a941fd476ce5ead); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaldf267884bfc4ded59a941fd476ce5ead)): ?>
<?php $component = $__componentOriginaldf267884bfc4ded59a941fd476ce5ead; ?>
<?php unset($__componentOriginaldf267884bfc4ded59a941fd476ce5ead); ?>
<?php endif; ?>
    </main>

</body>

</html><?php /**PATH C:\Users\<USER>\Desktop\dressup_project\resources\views/login.blade.php ENDPATH**/ ?>