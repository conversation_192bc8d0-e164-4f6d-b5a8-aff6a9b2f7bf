{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/components/clipboard/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAIA,OAAO,SAAS,MAAM,qBAAqB,CAAC;AAE5C,IAAM,OAAO,GAAyB;IAClC,YAAY,EAAE,KAAK;IACnB,WAAW,EAAE,OAAO;IACpB,MAAM,EAAE,cAAO,CAAC;CACnB,CAAC;AAEF,IAAM,sBAAsB,GAAoB;IAC5C,EAAE,EAAE,IAAI;IACR,QAAQ,EAAE,IAAI;CACjB,CAAC;AAEF;IASI,uBACI,SAAoC,EACpC,QAAwC,EACxC,OAAuC,EACvC,eAAyD;QAHzD,0BAAA,EAAA,gBAAoC;QACpC,yBAAA,EAAA,eAAwC;QACxC,wBAAA,EAAA,iBAAuC;QACvC,gCAAA,EAAA,wCAAyD;QAEzD,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC,EAAE;YACjC,CAAC,CAAC,eAAe,CAAC,EAAE;YACpB,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;QAElB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ,yBAAQ,OAAO,GAAK,OAAO,CAAE,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,SAAS,CAAC,WAAW,CACjB,eAAe,EACf,IAAI,EACJ,IAAI,CAAC,WAAW,EAChB,eAAe,CAAC,QAAQ,CAC3B,CAAC;IACN,CAAC;IAED,4BAAI,GAAJ;QAAA,iBAgBC;QAfG,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACzD,IAAI,CAAC,sBAAsB,GAAG;gBAC1B,KAAI,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC,CAAC;YAEF,8EAA8E;YAC9E,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAC5B,OAAO,EACP,IAAI,CAAC,sBAAsB,CAC9B,CAAC;aACL;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC5B;IACL,CAAC;IAED,+BAAO,GAAP;QACI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACxD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAC/B,OAAO,EACP,IAAI,CAAC,sBAAsB,CAC9B,CAAC;aACL;YACD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;SAC7B;IACL,CAAC;IAED,sCAAc,GAAd;QACI,SAAS,CAAC,cAAc,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAChE,CAAC;IAED,gDAAwB,GAAxB;QACI,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED,sCAAc,GAAd;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,OAAO,EAAE;YACvC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;SAC/B;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,WAAW,EAAE;YAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,KAAK,aAAa,EAAE;YAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;SACjE;IACL,CAAC;IAED,4BAAI,GAAJ;QACI,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvC,0CAA0C;QAC1C,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;YAC5B,sCAAsC;YACtC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;SAC5C;QAED,sCAAsC;QACtC,IAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACxD,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC;QAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAExC,mEAAmE;QACnE,YAAY,CAAC,MAAM,EAAE,CAAC;QACtB,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAE7B,gCAAgC;QAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAExC,oBAAoB;QACpB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE3B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,6CAA6C;IAC7C,kCAAU,GAAV,UAAW,IAAY;QACnB,IAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACpD,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,OAAO,QAAQ,CAAC,WAAW,CAAC;IAChC,CAAC;IAED,4CAAoB,GAApB,UAAqB,QAAoB;QACrC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;IACpC,CAAC;IACL,oBAAC;AAAD,CAAC,AA3HD,IA2HC;AAED,MAAM,UAAU,kBAAkB;IAC9B,QAAQ;SACH,gBAAgB,CAAC,iCAAiC,CAAC;SACnD,OAAO,CAAC,UAAC,UAAU;QAChB,IAAM,QAAQ,GAAG,UAAU,CAAC,YAAY,CACpC,+BAA+B,CAClC,CAAC;QACF,IAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAM,WAAW,GAAG,UAAU,CAAC,YAAY,CACvC,qCAAqC,CACxC,CAAC;QACF,IAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CACxC,sCAAsC,CACzC,CAAC;QAEF,qCAAqC;QACrC,IAAI,SAAS,EAAE;YACX,IACI,CAAC,SAAS,CAAC,cAAc,CACrB,eAAe,EACf,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAC/B,EACH;gBACE,IAAI,aAAa,CACb,UAAyB,EACzB,SAA6B,EAC7B;oBACI,YAAY,EACR,YAAY,IAAI,YAAY,KAAK,MAAM;wBACnC,CAAC,CAAC,IAAI;wBACN,CAAC,CAAC,OAAO,CAAC,YAAY;oBAC9B,WAAW,EAAE,WAAW;wBACpB,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,OAAO,CAAC,WAAW;iBACJ,CAC5B,CAAC;aACL;SACJ;aAAM;YACH,OAAO,CAAC,KAAK,CACT,uCAA+B,QAAQ,iFAA6E,CACvH,CAAC;SACL;IACL,CAAC,CAAC,CAAC;AACX,CAAC;AAED,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,MAAM,CAAC,cAAc,GAAG,kBAAkB,CAAC;CAC9C;AAED,eAAe,aAAa,CAAC"}