<?php

namespace App\Http\Controllers;

use App\Models\product_3d_model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ModelModifierController extends Controller
{
    public function getClippingData($id)
    {
        $model = product_3d_model::findOrFail($id);
        return response()->json($model->clipping_planes_data);
    }

    public function saveClippingData(Request $request)
    {
        $model = product_3d_model::findOrFail($request->model_id);
        $model->clipping_planes_data = ['planes' => $request->planes];
        $model->save();

        return response()->json(['status' => 'success']);
    }

    public function replaceModel(Request $request)
    {
        $model = product_3d_model::findOrFail($request->model_id);

        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('3d_models', 'public');
            Storage::disk('public')->delete($model->model_path);  // delete old
            $model->model_path = $path;
            $model->save();
        }

        return response()->json(['status' => 'success']);
    }
}
